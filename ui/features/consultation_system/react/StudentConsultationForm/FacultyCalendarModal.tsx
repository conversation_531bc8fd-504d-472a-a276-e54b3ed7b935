import React, { useState, useEffect } from 'react'
import { Modal } from '@instructure/ui-modal'
import { View } from '@instructure/ui-view'
import { Flex } from '@instructure/ui-flex'
import { Heading } from '@instructure/ui-heading'
import { But<PERSON> } from '@instructure/ui-buttons'
import { IconButton } from '@instructure/ui-buttons'
import { Text } from '@instructure/ui-text'
import { Spinner } from '@instructure/ui-spinner'
import CanvasSelect from '@canvas/instui-bindings/react/Select'
import { IconArrowOpenStartLine, IconArrowOpenEndLine } from '@instructure/ui-icons'
import type { FacultyUser } from '../types'

interface TimeSlot {
  id: string
  datetime: string
  formatted_time: string
  is_available: boolean
  is_booked: boolean
  faculty_id: string
  faculty_time_slot_id?: string
  created_at: string
  updated_at: string
}

interface FacultyCalendarModalProps {
  isOpen: boolean
  onClose: () => void
  faculty: FacultyUser | null
  onTimeSlotSelect: (datetime: string, facultyTimeSlotId?: string) => void
  selectedDateTime?: string
}

const FacultyCalendarModal: React.FC<FacultyCalendarModalProps> = ({
  isOpen,
  onClose,
  faculty,
  onTimeSlotSelect,
  selectedDateTime
}) => {
  const [currentWeek, setCurrentWeek] = useState(new Date())
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth())
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([])
  const [loading, setLoading] = useState(false)

  // Generate week dates
  const getWeekDates = (date: Date) => {
    const week = []
    const startOfWeek = new Date(date)
    const day = startOfWeek.getDay()
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1) // Monday as first day
    startOfWeek.setDate(diff)

    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek)
      day.setDate(startOfWeek.getDate() + i)
      week.push(day)
    }
    return week
  }

  const weekDates = getWeekDates(currentWeek)

  // Navigation functions
  const goToPreviousWeek = () => {
    const prevWeek = new Date(currentWeek)
    prevWeek.setDate(currentWeek.getDate() - 7)
    setCurrentWeek(prevWeek)
  }

  const goToNextWeek = () => {
    const nextWeek = new Date(currentWeek)
    nextWeek.setDate(currentWeek.getDate() + 7)
    setCurrentWeek(nextWeek)
  }

  const goToSelectedMonth = () => {
    const newDate = new Date(selectedYear, selectedMonth, 1)
    setCurrentWeek(newDate)
  }

  // Fetch time slots for the current week
  useEffect(() => {
    if (!faculty || !isOpen) return

    const fetchTimeSlots = async () => {
      setLoading(true)
      console.log('Starting to fetch time slots for faculty:', faculty.id)
      console.log('Week dates:', weekDates)

      try {
        const startDate = weekDates[0].toISOString().split('T')[0]
        const endDate = weekDates[6].toISOString().split('T')[0]
        const url = `/consultation_requests/faculty/${faculty.id}/time_slots?start_date=${startDate}&end_date=${endDate}`

        console.log('Fetching from URL:', url)

        const response = await fetch(url)
        console.log('Response status:', response.status)
        console.log('Response ok:', response.ok)

        if (response.ok) {
          const data = await response.json()
          console.log('Fetched time slots:', data.time_slots)
          console.log('Debug info:', data.debug_info)
          console.log('API Response URL:', response.url)
          console.log('Time slots count:', data.time_slots?.length || 0)
          setTimeSlots(data.time_slots || [])
        } else {
          const errorText = await response.text()
          console.error('Failed to fetch time slots. Status:', response.status)
          console.error('Error response:', errorText)

          // For testing/development - generate some sample slots if API fails
          const sampleSlots = generateSampleTimeSlots(faculty.id, weekDates)
          console.log('Using sample time slots due to API failure:', sampleSlots)
          setTimeSlots(sampleSlots)
        }
      } catch (error) {
        console.error('Error fetching time slots:', error)

        // For testing/development - generate some sample slots if API fails
        const sampleSlots = generateSampleTimeSlots(faculty.id, weekDates)
        console.log('Using sample time slots due to error:', sampleSlots)
        setTimeSlots(sampleSlots)
      } finally {
        setLoading(false)
      }
    }

    fetchTimeSlots()
  }, [faculty, currentWeek, isOpen])

  // Helper function to generate sample time slots for testing/development
  const generateSampleTimeSlots = (facultyId: string, dates: Date[]) => {
    const slots: TimeSlot[] = []

    // Generate 3 sample slots per day
    dates.forEach(date => {
      // Skip weekends for sample data
      if (date.getDay() === 0 || date.getDay() === 6) return

      // Morning slot (9:00 AM)
      const morningSlot = new Date(date)
      morningSlot.setHours(9, 0, 0)

      // Afternoon slot (2:00 PM)
      const afternoonSlot = new Date(date)
      afternoonSlot.setHours(14, 0, 0)

      // Evening slot (4:30 PM)
      const eveningSlot = new Date(date)
      eveningSlot.setHours(16, 30, 0)

      // Add slots with different statuses
      slots.push({
        id: `sample-morning-${date.toISOString()}`,
        datetime: morningSlot.toISOString(),
        formatted_time: '9:00 AM',
        is_available: true,
        is_booked: false,
        faculty_id: facultyId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })

      slots.push({
        id: `sample-afternoon-${date.toISOString()}`,
        datetime: afternoonSlot.toISOString(),
        formatted_time: '2:00 PM',
        is_available: true,
        is_booked: date.getDate() % 2 === 0, // Every other day is booked
        faculty_id: facultyId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
    })

    return slots
  }

  // Generate time slots for display (7 AM to 8 PM)
  const generateTimeSlots = () => {
    const slots = []
    for (let hour = 7; hour <= 20; hour++) {
      slots.push(`${hour.toString().padStart(2, '0')}:00`)
      if (hour < 20) {
        slots.push(`${hour.toString().padStart(2, '0')}:30`)
      }
    }
    return slots
  }

  const timeSlotHours = generateTimeSlots()

  // Get slots for specific date and time
  const getSlotsForDateTime = (date: Date, time: string) => {
    const dateStr = date.toISOString().split('T')[0]
    const [hour, minute] = time.split(':')

    const matchingSlots = timeSlots.filter(slot => {
      const slotDate = new Date(slot.datetime)
      const matches = (
        slotDate.toISOString().split('T')[0] === dateStr &&
        slotDate.getHours() === parseInt(hour) &&
        slotDate.getMinutes() === parseInt(minute)
      )

      return matches
    })

    return matchingSlots
  }

  // Handle time slot selection
  const handleTimeSlotClick = (slot: TimeSlot) => {
    if (slot.is_available && !slot.is_booked) {
      onTimeSlotSelect(slot.datetime, slot.faculty_time_slot_id || slot.id)
    }
  }

  // Format date for display
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    })
  }

  // Generate month options
  const monthOptions = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ]

  // Generate year options (current year ± 2)
  const currentYear = new Date().getFullYear()
  const yearOptions = []
  for (let year = currentYear - 1; year <= currentYear + 2; year++) {
    yearOptions.push(year)
  }

  return (
    <Modal
      open={isOpen}
      onDismiss={onClose}
      size="large"
      label="Faculty Consultation Calendar"
      shouldCloseOnDocumentClick={false}
    >
      <Modal.Header>
        <Heading level="h3">
          Select Consultation Time - {faculty?.name}
        </Heading>
      </Modal.Header>

      <Modal.Body>
        <View as="div" padding="medium">
          {/* Calendar Navigation */}
          <Flex justifyItems="space-between" alignItems="center" margin="0 0 medium 0">
            <Flex.Item>
              <Flex gap="small" alignItems="center">
                <IconButton
                  screenReaderLabel="Previous week"
                  onClick={goToPreviousWeek}
                  size="small"
                >
                  <IconArrowOpenStartLine />
                </IconButton>

                <Text weight="bold" size="large">
                  {weekDates[0].toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                </Text>

                <IconButton
                  screenReaderLabel="Next week"
                  onClick={goToNextWeek}
                  size="small"
                >
                  <IconArrowOpenEndLine />
                </IconButton>
              </Flex>
            </Flex.Item>

            <Flex.Item>
              <Flex gap="small" alignItems="center">
                <View as="div" margin="0 small 0 0">
                  <CanvasSelect
                    id="month-select"
                    label="Month"
                    value={selectedMonth.toString()}
                    onChange={(_e, value) => {
                      setSelectedMonth(parseInt(value))
                    }}
                  >
                    {monthOptions.map((month, index) => (
                      <CanvasSelect.Option key={index} id={index.toString()} value={index.toString()}>
                        {month}
                      </CanvasSelect.Option>
                    ))}
                  </CanvasSelect>
                </View>

                <View as="div" margin="0 small 0 0">
                  <CanvasSelect
                    id="year-select"
                    label="Year"
                    value={selectedYear.toString()}
                    onChange={(_e, value) => setSelectedYear(parseInt(value))}
                  >
                    {yearOptions.map(year => (
                      <CanvasSelect.Option key={year} id={year.toString()} value={year.toString()}>
                        {year.toString()}
                      </CanvasSelect.Option>
                    ))}
                  </CanvasSelect>
                </View>

                <Button onClick={goToSelectedMonth} size="small">
                  Go
                </Button>
              </Flex>
            </Flex.Item>
          </Flex>

          {/* Calendar Grid */}
          <View as="div" borderWidth="small" borderRadius="medium">
            {/* Week Header */}
            <View as="div" background="secondary" borderWidth="0 0 small 0">
              <Flex>
                <Flex.Item size="80px" padding="small" textAlign="center">
                  <Text weight="bold" size="small">Time</Text>
                </Flex.Item>
                {weekDates.map((date, index) => (
                  <Flex.Item key={index} shouldGrow padding="small" textAlign="center" width={120}>
                    <Text weight="bold" size="small">
                      {formatDate(date)}
                    </Text>
                  </Flex.Item>
                ))}
              </Flex>
            </View>

            {/* Time Slots Grid */}
            {loading ? (
              <View as="div" padding="large" textAlign="center">
                <Spinner renderTitle="Loading time slots..." />
              </View>
            ) : (
              <View as="div" maxHeight="400px" overflowY="auto">
                {timeSlotHours.map((time, timeIndex) => (
                  <View key={timeIndex} as="div" borderWidth="0 0 small 0" minHeight="40px">
                    <Flex alignItems="center">
                      <Flex.Item size="80px" padding="x-small" textAlign="center">
                        <Text size="small" weight="normal">{time}</Text>
                      </Flex.Item>
                      {weekDates.map((date, dateIndex) => {
                        const slotsAtTime = getSlotsForDateTime(date, time)
                        const hasSlot = slotsAtTime.length > 0
                        const slot = slotsAtTime[0] // Take first slot if multiple
                        const isPast = new Date(`${date.toISOString().split('T')[0]}T${time}:00`) < new Date()
                        const isSelected = selectedDateTime === slot?.datetime

                        return (
                          <Flex.Item
                            key={dateIndex}
                            shouldGrow
                            padding="x-small"
                            textAlign="center"
                            width={120}
                          >
                            {hasSlot ? (
                              <Button
                                size="small"
                                onClick={() => handleTimeSlotClick(slot)}
                                color={
                                  isSelected ? "primary" :
                                  slot.is_booked ? "danger" :
                                  slot.is_available ? "success" : "secondary"
                                }
                                disabled={!slot.is_available || slot.is_booked || isPast}
                                display="block"
                                textAlign="center"
                                width="100%"
                                height="32px"
                              >
                                <Text size="x-small" color="primary-inverse">
                                  {isSelected ? "Selected" :
                                   slot.is_booked ? "Booked" :
                                   slot.is_available ? "Available" : "Unavailable"}
                                </Text>
                              </Button>
                            ) : (
                              <View
                                as="div"
                                height="32px"
                                background={isPast ? "secondary" : "primary"}
                                borderRadius="small"
                                textAlign="center"
                                padding="x-small"
                                width="100%"
                              >
                                <Flex justifyItems="center" alignItems="center" height="100%">
                                  <Flex.Item textAlign='center' width={120}>
                                    <Text size="x-small" color={isPast ? "secondary" : "primary"}>
                                      {isPast ? "Past" : "No Slot"}
                                    </Text>
                                  </Flex.Item>
                                </Flex>
                              </View>
                            )}
                          </Flex.Item>
                        )
                      })}
                    </Flex>
                  </View>
                ))}
              </View>
            )}
          </View>

          {/* Legend */}
          <Flex gap="medium" margin="medium 0 0 0" justifyItems="center">
            <Flex.Item>
              <Flex gap="x-small" alignItems="center">
                <View as="div" width="16px" height="16px" background="success" borderRadius="small" />
                <Text size="small">Available</Text>
              </Flex>
            </Flex.Item>
            <Flex.Item>
              <Flex gap="x-small" alignItems="center">
                <View as="div" width="16px" height="16px" background="danger" borderRadius="small" />
                <Text size="small">Booked</Text>
              </Flex>
            </Flex.Item>
            <Flex.Item>
              <Flex gap="x-small" alignItems="center">
                <View as="div" width="16px" height="16px" background="primary" borderRadius="small" />
                <Text size="small">Selected</Text>
              </Flex>
            </Flex.Item>
            <Flex.Item>
              <Flex gap="x-small" alignItems="center">
                <View as="div" width="16px" height="16px" background="secondary" borderRadius="small" />
                <Text size="small">Past</Text>
              </Flex>
            </Flex.Item>
          </Flex>
        </View>
      </Modal.Body>

      <Modal.Footer>
        <Button onClick={onClose}>Close</Button>
      </Modal.Footer>
    </Modal>
  )
}

export default FacultyCalendarModal